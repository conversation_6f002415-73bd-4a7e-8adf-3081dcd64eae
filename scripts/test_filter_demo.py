#!/usr/bin/env python3
"""
IMU滤波演示脚本
生成模拟的IMU数据并演示滤波效果
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from datetime import datetime

def generate_simulated_imu_data(duration=10.0, sample_rate=100.0):
    """生成模拟的IMU数据"""

    # 时间轴
    t = np.arange(0, duration, 1.0/sample_rate)

    # 生成基础信号（低频运动）
    base_angular_velocity = 0.5 * np.sin(2 * np.pi * 0.2 * t) + 0.3 * np.sin(2 * np.pi * 0.5 * t)
    base_linear_acceleration = 0.8 * np.sin(2 * np.pi * 0.3 * t) + 0.4 * np.cos(2 * np.pi * 0.7 * t)

    # 添加高频噪声
    angular_noise = 0.1 * np.random.normal(0, 1, len(t)) + 0.05 * np.sin(2 * np.pi * 20 * t)
    linear_noise = 0.2 * np.random.normal(0, 1, len(t)) + 0.1 * np.sin(2 * np.pi * 15 * t)

    # 合成最终信号
    raw_angular_velocity = base_angular_velocity + angular_noise
    raw_linear_acceleration = base_linear_acceleration + linear_noise

    # 时间戳（毫秒）
    timestamps = (t * 1000).astype(int)

    return timestamps, raw_angular_velocity, raw_linear_acceleration

def apply_low_pass_filter(data, alpha=0.1):
    """应用低通滤波器"""
    filtered_data = np.zeros_like(data)
    filtered_data[0] = data[0]  # 初始化

    for i in range(1, len(data)):
        filtered_data[i] = alpha * data[i] + (1 - alpha) * filtered_data[i-1]

    return filtered_data

def create_demo_csv(output_path="./demo_filter_data.csv"):
    """创建演示用的CSV文件"""

    # 生成模拟数据
    timestamps, raw_angular_vel, raw_linear_accel = generate_simulated_imu_data()

    # 应用滤波
    filtered_angular_vel = apply_low_pass_filter(raw_angular_vel, alpha=0.1)
    filtered_linear_accel = apply_low_pass_filter(raw_linear_accel, alpha=0.1)

    # 创建DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'raw_angular_velocity': raw_angular_vel,
        'filtered_angular_velocity': filtered_angular_vel,
        'raw_linear_acceleration': raw_linear_accel,
        'filtered_linear_acceleration': filtered_linear_accel
    })

    # 保存CSV文件
    df.to_csv(output_path, index=False)
    print(f"演示数据已保存到: {output_path}")

    return df

def plot_demo_comparison(df, output_dir="./"):
    """绘制演示对比图"""

    # 创建时间轴（秒）
    time_seconds = df['timestamp'].values / 1000.0

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('IMU低通滤波演示 (α=0.1)', fontsize=16, fontweight='bold')

    # 1. 角速度对比
    axes[0, 0].plot(time_seconds, df['raw_angular_velocity'].values, 'b-', alpha=0.7, linewidth=1, label='原始角速度')
    axes[0, 0].plot(time_seconds, df['filtered_angular_velocity'].values, 'r-', linewidth=2, label='滤波后角速度')
    axes[0, 0].set_title('角速度滤波效果', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('时间 (秒)')
    axes[0, 0].set_ylabel('角速度 (rad/s)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 线性加速度对比
    axes[0, 1].plot(time_seconds, df['raw_linear_acceleration'].values, 'b-', alpha=0.7, linewidth=1, label='原始线性加速度')
    axes[0, 1].plot(time_seconds, df['filtered_linear_acceleration'].values, 'r-', linewidth=2, label='滤波后线性加速度')
    axes[0, 1].set_title('线性加速度滤波效果', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('时间 (秒)')
    axes[0, 1].set_ylabel('线性加速度 (m/s²)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 角速度局部放大
    start_idx = len(df) // 4
    end_idx = start_idx + 200
    axes[1, 0].plot(time_seconds[start_idx:end_idx], df['raw_angular_velocity'].values[start_idx:end_idx],
                   'b-', alpha=0.7, linewidth=1, label='原始角速度')
    axes[1, 0].plot(time_seconds[start_idx:end_idx], df['filtered_angular_velocity'].values[start_idx:end_idx],
                   'r-', linewidth=2, label='滤波后角速度')
    axes[1, 0].set_title('角速度局部放大', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('时间 (秒)')
    axes[1, 0].set_ylabel('角速度 (rad/s)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 线性加速度局部放大
    axes[1, 1].plot(time_seconds[start_idx:end_idx], df['raw_linear_acceleration'].values[start_idx:end_idx],
                   'b-', alpha=0.7, linewidth=1, label='原始线性加速度')
    axes[1, 1].plot(time_seconds[start_idx:end_idx], df['filtered_linear_acceleration'].values[start_idx:end_idx],
                   'r-', linewidth=2, label='滤波后线性加速度')
    axes[1, 1].set_title('线性加速度局部放大', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('时间 (秒)')
    axes[1, 1].set_ylabel('线性加速度 (m/s²)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"imu_filter_demo_{timestamp}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"演示图片已保存: {output_file}")

    plt.show()

def print_demo_statistics(df):
    """打印演示统计信息"""
    print("\n=== 滤波效果统计 ===")

    # 角速度统计
    raw_angular_std = df['raw_angular_velocity'].std()
    filtered_angular_std = df['filtered_angular_velocity'].std()
    angular_noise_reduction = (1 - filtered_angular_std / raw_angular_std) * 100

    print(f"角速度:")
    print(f"  原始标准差:     {raw_angular_std:.6f} rad/s")
    print(f"  滤波后标准差:   {filtered_angular_std:.6f} rad/s")
    print(f"  噪声降低:       {angular_noise_reduction:.2f}%")

    # 线性加速度统计
    raw_linear_std = df['raw_linear_acceleration'].std()
    filtered_linear_std = df['filtered_linear_acceleration'].std()
    linear_noise_reduction = (1 - filtered_linear_std / raw_linear_std) * 100

    print(f"\n线性加速度:")
    print(f"  原始标准差:     {raw_linear_std:.6f} m/s²")
    print(f"  滤波后标准差:   {filtered_linear_std:.6f} m/s²")
    print(f"  噪声降低:       {linear_noise_reduction:.2f}%")

    # 延迟分析
    angular_delay = np.argmax(np.correlate(df['filtered_angular_velocity'].values, df['raw_angular_velocity'].values, mode='full')) - len(df) + 1
    linear_delay = np.argmax(np.correlate(df['filtered_linear_acceleration'].values, df['raw_linear_acceleration'].values, mode='full')) - len(df) + 1

    print(f"\n滤波延迟:")
    print(f"  角速度延迟:     {angular_delay} 采样点")
    print(f"  线性加速度延迟: {linear_delay} 采样点")

def main():
    print("=== IMU低通滤波演示 ===")
    print("生成模拟IMU数据并演示滤波效果...")

    # 创建输出目录
    output_dir = "./filter_demo_output"
    os.makedirs(output_dir, exist_ok=True)

    # 生成演示数据
    csv_path = os.path.join(output_dir, "demo_filter_data.csv")
    df = create_demo_csv(csv_path)

    # 打印统计信息
    print_demo_statistics(df)

    # 绘制对比图
    plot_demo_comparison(df, output_dir)

    print(f"\n演示完成！输出文件保存在: {output_dir}")
    print("\n滤波器说明:")
    print("- 使用一阶低通滤波器: y[n] = α*x[n] + (1-α)*y[n-1]")
    print("- 滤波系数 α = 0.1 (较强滤波，适合去除高频噪声)")
    print("- α 越小滤波越强，但响应越慢")
    print("- α 越大响应越快，但滤波效果越弱")

if __name__ == "__main__":
    main()
