#!/usr/bin/env python3
"""
IMU滤波数据可视化脚本
用于显示滤波前后的角速度和线性加速度数据对比
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os
from datetime import datetime

def load_filter_data(csv_file):
    """加载滤波数据CSV文件"""
    try:
        df = pd.read_csv(csv_file)
        print(f"成功加载数据文件: {csv_file}")
        print(f"数据点数量: {len(df)}")
        print(f"数据列: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"加载数据文件失败: {e}")
        return None

def plot_filter_comparison(df, output_dir="./"):
    """绘制滤波前后的数据对比图"""

    # 创建时间轴（转换时间戳为相对时间，单位：秒）
    if len(df) > 0:
        start_time = df['timestamp'].iloc[0]
        time_seconds = (df['timestamp'].values - start_time) / 1000.0  # 转换为秒
    else:
        print("数据为空，无法绘图")
        return

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('IMU数据滤波效果对比', fontsize=16, fontweight='bold')

    # 1. 角速度对比
    axes[0, 0].plot(time_seconds, df['raw_angular_velocity'].values, 'b-', alpha=0.7, linewidth=1, label='原始角速度')
    axes[0, 0].plot(time_seconds, df['filtered_angular_velocity'].values, 'r-', linewidth=2, label='滤波后角速度')
    axes[0, 0].set_title('角速度对比 (rad/s)', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('时间 (秒)')
    axes[0, 0].set_ylabel('角速度 (rad/s)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 线性加速度对比
    axes[0, 1].plot(time_seconds, df['raw_linear_acceleration'].values, 'b-', alpha=0.7, linewidth=1, label='原始线性加速度')
    axes[0, 1].plot(time_seconds, df['filtered_linear_acceleration'].values, 'r-', linewidth=2, label='滤波后线性加速度')
    axes[0, 1].set_title('线性加速度对比 (m/s²)', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('时间 (秒)')
    axes[0, 1].set_ylabel('线性加速度 (m/s²)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 角速度噪声分析
    angular_noise = df['raw_angular_velocity'].values - df['filtered_angular_velocity'].values
    axes[1, 0].plot(time_seconds, angular_noise, 'g-', alpha=0.8, linewidth=1)
    axes[1, 0].set_title('角速度噪声 (原始 - 滤波)', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('时间 (秒)')
    axes[1, 0].set_ylabel('噪声 (rad/s)')
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 线性加速度噪声分析
    linear_noise = df['raw_linear_acceleration'].values - df['filtered_linear_acceleration'].values
    axes[1, 1].plot(time_seconds, linear_noise, 'g-', alpha=0.8, linewidth=1)
    axes[1, 1].set_title('线性加速度噪声 (原始 - 滤波)', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('时间 (秒)')
    axes[1, 1].set_ylabel('噪声 (m/s²)')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"imu_filter_comparison_{timestamp}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"图片已保存: {output_file}")

    plt.show()

def plot_frequency_analysis(df, output_dir="./"):
    """绘制频域分析图"""

    # 计算采样频率
    if len(df) > 1:
        dt = (df['timestamp'].iloc[1] - df['timestamp'].iloc[0]) / 1000.0  # 转换为秒
        fs = 1.0 / dt  # 采样频率
    else:
        print("数据点不足，无法进行频域分析")
        return

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    fig, axes = plt.subplots(2, 1, figsize=(12, 8))
    fig.suptitle('IMU数据频域分析', fontsize=16, fontweight='bold')

    # 角速度频域分析
    raw_angular_fft = np.fft.fft(df['raw_angular_velocity'].values)
    filtered_angular_fft = np.fft.fft(df['filtered_angular_velocity'].values)
    freqs = np.fft.fftfreq(len(df), dt)

    # 只显示正频率部分
    positive_freqs = freqs[:len(freqs)//2]
    raw_angular_magnitude = np.abs(raw_angular_fft[:len(freqs)//2])
    filtered_angular_magnitude = np.abs(filtered_angular_fft[:len(freqs)//2])

    axes[0].semilogy(positive_freqs, raw_angular_magnitude, 'b-', alpha=0.7, label='原始角速度')
    axes[0].semilogy(positive_freqs, filtered_angular_magnitude, 'r-', linewidth=2, label='滤波后角速度')
    axes[0].set_title('角速度频域分析', fontsize=12, fontweight='bold')
    axes[0].set_xlabel('频率 (Hz)')
    axes[0].set_ylabel('幅值')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    axes[0].set_xlim(0, min(50, fs/2))  # 限制显示频率范围

    # 线性加速度频域分析
    raw_linear_fft = np.fft.fft(df['raw_linear_acceleration'].values)
    filtered_linear_fft = np.fft.fft(df['filtered_linear_acceleration'].values)

    raw_linear_magnitude = np.abs(raw_linear_fft[:len(freqs)//2])
    filtered_linear_magnitude = np.abs(filtered_linear_fft[:len(freqs)//2])

    axes[1].semilogy(positive_freqs, raw_linear_magnitude, 'b-', alpha=0.7, label='原始线性加速度')
    axes[1].semilogy(positive_freqs, filtered_linear_magnitude, 'r-', linewidth=2, label='滤波后线性加速度')
    axes[1].set_title('线性加速度频域分析', fontsize=12, fontweight='bold')
    axes[1].set_xlabel('频率 (Hz)')
    axes[1].set_ylabel('幅值')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    axes[1].set_xlim(0, min(50, fs/2))  # 限制显示频率范围

    plt.tight_layout()

    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"imu_frequency_analysis_{timestamp}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"频域分析图已保存: {output_file}")

    plt.show()

def print_statistics(df):
    """打印统计信息"""
    print("\n=== 数据统计信息 ===")

    # 角速度统计
    raw_angular_std = df['raw_angular_velocity'].std()
    filtered_angular_std = df['filtered_angular_velocity'].std()
    angular_noise_reduction = (1 - filtered_angular_std / raw_angular_std) * 100

    print(f"角速度标准差:")
    print(f"  原始数据: {raw_angular_std:.6f} rad/s")
    print(f"  滤波后:   {filtered_angular_std:.6f} rad/s")
    print(f"  噪声降低: {angular_noise_reduction:.2f}%")

    # 线性加速度统计
    raw_linear_std = df['raw_linear_acceleration'].std()
    filtered_linear_std = df['filtered_linear_acceleration'].std()
    linear_noise_reduction = (1 - filtered_linear_std / raw_linear_std) * 100

    print(f"\n线性加速度标准差:")
    print(f"  原始数据: {raw_linear_std:.6f} m/s²")
    print(f"  滤波后:   {filtered_linear_std:.6f} m/s²")
    print(f"  噪声降低: {linear_noise_reduction:.2f}%")

def main():
    parser = argparse.ArgumentParser(description='IMU滤波数据可视化工具')
    parser.add_argument('--input', '-i',
                       default='/userdata/log/stuck_recovery_filter_data.csv',
                       help='输入CSV文件路径')
    parser.add_argument('--output', '-o',
                       default='./',
                       help='输出图片目录')
    parser.add_argument('--frequency', '-f',
                       action='store_true',
                       help='是否进行频域分析')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        print("请确保脱困检测系统已运行并生成了数据文件")
        return

    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)

    # 加载数据
    df = load_filter_data(args.input)
    if df is None:
        return

    # 检查数据完整性
    required_columns = ['timestamp', 'raw_angular_velocity', 'filtered_angular_velocity',
                       'raw_linear_acceleration', 'filtered_linear_acceleration']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要的数据列: {missing_columns}")
        return

    # 打印统计信息
    print_statistics(df)

    # 绘制时域对比图
    plot_filter_comparison(df, args.output)

    # 绘制频域分析图（可选）
    if args.frequency:
        plot_frequency_analysis(df, args.output)

if __name__ == "__main__":
    main()
